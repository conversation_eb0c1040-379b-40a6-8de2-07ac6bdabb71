"use server";

import { getSession } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { isValidArea, type AreaType } from "@/lib/areaConstants";

/**
 * Verifica si el usuario actual tiene acceso a un área específica
 */
export async function hasAreaAccess(areaName: string): Promise<boolean> {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return false;
    }

    // Validar que el área es válida
    if (!isValidArea(areaName)) {
      return false;
    }

    // Los ADMIN y SUPER_ADMIN tienen acceso a todas las áreas
    if (["ADMIN", "SUPER_ADMIN"].includes(session.user.role)) {
      return true;
    }

    // Para usuarios WORKER, verificar si tienen el área asignada
    if (session.user.role === "WORKER") {
      const user = await prisma.user.findUnique({
        where: { id: session.user.id },
        include: { area: true }
      });

      if (!user || !user.area) {
        return false;
      }

      return user.area.nombre === areaName;
    }

    // Otros roles no tienen acceso por defecto
    return false;
  } catch (error) {
    console.error("Error al verificar acceso al área:", error);
    return false;
  }
}

/**
 * Obtiene las áreas a las que el usuario actual tiene acceso
 */
export async function getUserAccessibleAreas(): Promise<AreaType[]> {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return [];
    }

    // Los ADMIN y SUPER_ADMIN tienen acceso a todas las áreas
    if (["ADMIN", "SUPER_ADMIN"].includes(session.user.role)) {
      // Obtener todas las áreas activas
      const areas = await prisma.area.findMany({
        where: { activo: true },
        orderBy: { orden: 'asc' }
      });
      return areas.map(area => area.nombre as AreaType);
    }

    // Para usuarios WORKER, solo su área asignada
    if (session.user.role === "WORKER") {
      const user = await prisma.user.findUnique({
        where: { id: session.user.id },
        include: { area: true }
      });

      if (!user || !user.area) {
        return [];
      }

      return [user.area.nombre as AreaType];
    }

    // Otros roles no tienen acceso por defecto
    return [];
  } catch (error) {
    console.error("Error al obtener áreas accesibles:", error);
    return [];
  }
}

/**
 * Middleware para verificar acceso a un área específica
 * Lanza un error si el usuario no tiene acceso
 */
export async function requireAreaAccess(areaName: string): Promise<void> {
  const hasAccess = await hasAreaAccess(areaName);
  
  if (!hasAccess) {
    throw new Error(`Acceso denegado al área: ${areaName}`);
  }
}

/**
 * Verifica si el usuario actual es WORKER y tiene un área asignada
 */
export async function isWorkerWithArea(): Promise<{ isWorker: boolean; hasArea: boolean; areaName?: string }> {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return { isWorker: false, hasArea: false };
    }

    if (session.user.role !== "WORKER") {
      return { isWorker: false, hasArea: false };
    }

    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: { area: true }
    });

    if (!user) {
      return { isWorker: true, hasArea: false };
    }

    return {
      isWorker: true,
      hasArea: !!user.area,
      areaName: user.area?.nombre
    };
  } catch (error) {
    console.error("Error al verificar estado de worker:", error);
    return { isWorker: false, hasArea: false };
  }
}

/**
 * Obtiene información del área del usuario actual (solo para WORKERS)
 */
export async function getCurrentWorkerArea(): Promise<{ success: boolean; area?: any; error?: string }> {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return { success: false, error: "No autenticado" };
    }

    if (session.user.role !== "WORKER") {
      return { success: false, error: "Solo disponible para usuarios WORKER" };
    }

    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: { area: true }
    });

    if (!user) {
      return { success: false, error: "Usuario no encontrado" };
    }

    if (!user.area) {
      return { success: false, error: "Usuario sin área asignada" };
    }

    return { success: true, area: user.area };
  } catch (error) {
    console.error("Error al obtener área del worker:", error);
    return { success: false, error: "Error interno del servidor" };
  }
}

/**
 * Filtra las pestañas del dashboard según el acceso del usuario
 */
export async function getAccessibleTabs(): Promise<string[]> {
  try {
    const accessibleAreas = await getUserAccessibleAreas();
    return accessibleAreas;
  } catch (error) {
    console.error("Error al obtener pestañas accesibles:", error);
    return [];
  }
}
